"""
Test Script for Integer-based LeNet-5 on MNIST
==============================================
This script tests the integer-based LeNet-5 model on MNIST dataset
and compares the results with the original quantized model.
"""

import os
import argparse
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import torchvision.transforms as transforms
from torchvision.datasets import MNIST
import matplotlib.pyplot as plt
import numpy as np
from tqdm import tqdm
from sklearn.metrics import confusion_matrix, classification_report

from model import get_model
from int_inference import IntLeNet5


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Integer-based LeNet-5 Testing')
    parser.add_argument('--batch-size', type=int, default=100, help='Batch size for testing')
    parser.add_argument('--checkpoint', type=str, default='checkpoints/model_best.pth',
                        help='Path to model checkpoint')
    parser.add_argument('--params-file', type=str,
                        default='extracted_params/parameters.json',
                        help='Path to integer parameters JSON file')
    parser.add_argument('--output-dir', type=str, default='results/int_inference',
                        help='Directory to save results')
    parser.add_argument('--visualize', action='store_true', help='Visualize results')
    parser.add_argument('--num-samples', type=int, default=1000,
                        help='Number of samples to test (use -1 for all)')
    return parser.parse_args()


def float_to_int8(x, scale=0.0219416506588459):
    """
    Convert floating-point tensor to INT8 tensor.

    Args:
        x: Floating-point tensor
        scale: Scale factor for quantization

    Returns:
        INT8 tensor
    """
    # Quantize to INT8 range
    x_int = np.round(x.numpy() / scale).astype(np.int8)
    return x_int


def test_models(original_model, int_model, test_loader, device, scale, num_samples=-1):
    """
    Test both original and integer-based models on the same data.

    Args:
        original_model: Original quantized PyTorch model
        int_model: Integer-based model
        test_loader: DataLoader for test data
        device: Device to run the original model on
        scale: Scale factor for quantization
        num_samples: Number of samples to test (-1 for all)

    Returns:
        tuple: (Original model predictions, Integer model predictions, Targets)
    """
    original_model.eval()

    original_preds = []
    int_preds = []
    targets = []

    correct_original = 0
    correct_int = 0
    total = 0

    with torch.no_grad():
        for batch_idx, (data, target) in enumerate(tqdm(test_loader, desc='Testing')):
            # Move data to device for original model
            data_orig, target_orig = data.to(device), target.to(device)

            # Forward pass with original model
            output_orig = original_model(data_orig)

            # If output is a QuantTensor, extract the tensor value
            if hasattr(output_orig, 'value'):
                output_orig = output_orig.value

            # Get predictions from original model
            _, pred_orig = output_orig.max(1)

            # Convert input to INT8 for integer model
            data_int = float_to_int8(data, scale)

            # Forward pass with integer model
            output_int = int_model.forward(data_int)

            # Get predictions from integer model
            pred_int = np.argmax(output_int, axis=1)

            # Update counters
            total += target.size(0)
            correct_original += (pred_orig.cpu().numpy() == target.numpy()).sum()
            correct_int += (pred_int == target.numpy()).sum()

            # Store predictions and targets
            original_preds.extend(pred_orig.cpu().numpy())
            int_preds.extend(pred_int)
            targets.extend(target.numpy())

            # Break if we've processed enough samples
            if num_samples > 0 and total >= num_samples:
                break

    # Calculate accuracy
    accuracy_original = 100 * correct_original / total
    accuracy_int = 100 * correct_int / total

    print(f'Original model accuracy: {accuracy_original:.2f}%')
    print(f'Integer model accuracy: {accuracy_int:.2f}%')

    return np.array(original_preds), np.array(int_preds), np.array(targets)


def visualize_results(original_preds, int_preds, targets, output_dir):
    """
    Visualize test results.

    Args:
        original_preds: Predictions from original model
        int_preds: Predictions from integer model
        targets: Ground truth labels
        output_dir: Directory to save visualizations
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Create confusion matrix for integer model
    cm = confusion_matrix(targets, int_preds)
    plt.figure(figsize=(10, 8))
    plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
    plt.title('Confusion Matrix (Integer Model)')
    plt.colorbar()

    classes = [str(i) for i in range(10)]
    tick_marks = np.arange(len(classes))
    plt.xticks(tick_marks, classes)
    plt.yticks(tick_marks, classes)

    # Add text annotations
    thresh = cm.max() / 2.
    for i in range(cm.shape[0]):
        for j in range(cm.shape[1]):
            plt.text(j, i, format(cm[i, j], 'd'),
                     horizontalalignment="center",
                     color="white" if cm[i, j] > thresh else "black")

    plt.tight_layout()
    plt.ylabel('True label')
    plt.xlabel('Predicted label')

    # Save the figure
    plt.savefig(os.path.join(output_dir, 'confusion_matrix_int.png'))
    plt.close()

    # Print classification report for integer model
    report = classification_report(targets, int_preds, target_names=classes)
    print("Classification Report (Integer Model):")
    print(report)

    # Save classification report
    with open(os.path.join(output_dir, 'classification_report_int.txt'), 'w') as f:
        f.write(report)

    # Compare predictions between models
    agreement = np.mean(original_preds == int_preds) * 100
    print(f"Agreement between models: {agreement:.2f}%")

    # Save agreement to file
    with open(os.path.join(output_dir, 'model_agreement.txt'), 'w') as f:
        f.write(f"Agreement between original and integer models: {agreement:.2f}%\n")


def visualize_samples(test_loader, original_model, int_model, device, scale, output_dir, num_samples=5):
    """
    Visualize sample predictions from both models.

    Args:
        test_loader: DataLoader for test data
        original_model: Original quantized PyTorch model
        int_model: Integer-based model
        device: Device to run the original model on
        scale: Scale factor for quantization
        output_dir: Directory to save visualizations
        num_samples: Number of samples to visualize
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    original_model.eval()

    # Get some random test images
    dataiter = iter(test_loader)
    images, labels = next(dataiter)

    # Create figure
    fig, axes = plt.subplots(num_samples, 3, figsize=(12, 3*num_samples))

    with torch.no_grad():
        for i in range(num_samples):
            # Get image and label
            img = images[i:i+1]
            label = labels[i].item()

            # Original model prediction
            output_orig = original_model(img.to(device))
            if hasattr(output_orig, 'value'):
                output_orig = output_orig.value
            pred_orig = output_orig.argmax(1).item()

            # Integer model prediction
            img_int = float_to_int8(img, scale)
            output_int = int_model.forward(img_int)
            pred_int = np.argmax(output_int, axis=1)[0]

            # Display original image
            axes[i, 0].imshow(img[0, 0].numpy(), cmap='gray')
            axes[i, 0].set_title(f'True: {label}')
            axes[i, 0].axis('off')

            # Display original model prediction
            axes[i, 1].imshow(img[0, 0].numpy(), cmap='gray')
            axes[i, 1].set_title(f'Original: {pred_orig}')
            axes[i, 1].axis('off')

            # Display integer model prediction
            axes[i, 2].imshow(img[0, 0].numpy(), cmap='gray')
            axes[i, 2].set_title(f'Integer: {pred_int}')
            axes[i, 2].axis('off')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'sample_predictions.png'))
    plt.close()


def main():
    """Main function."""
    args = parse_args()

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')

    # Data transformations
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.1307,), (0.3081,))
    ])

    # Load MNIST dataset
    test_dataset = MNIST('./data', train=False, download=True, transform=transform)

    # Create data loader
    test_loader = DataLoader(
        test_dataset, batch_size=args.batch_size, shuffle=False, num_workers=4, pin_memory=True
    )

    # Load original model
    original_model = get_model().to(device)

    # Load checkpoint for original model
    if os.path.isfile(args.checkpoint):
        print(f'Loading checkpoint {args.checkpoint}')
        checkpoint = torch.load(args.checkpoint, map_location=device)
        original_model.load_state_dict(checkpoint['state_dict'])
        print(f'Loaded checkpoint {args.checkpoint} (epoch {checkpoint["epoch"]})')
    else:
        print(f'No checkpoint found at {args.checkpoint}')
        return

    # Load integer-based model
    print(f'Loading integer parameters from {args.params_file}')
    int_model = IntLeNet5(args.params_file)
    print('Integer model loaded')

    # Scale factor for quantization
    scale = 0.0219416506588459

    # Test both models
    original_preds, int_preds, targets = test_models(
        original_model, int_model, test_loader, device, scale, args.num_samples
    )

    # Visualize results if specified
    if args.visualize:
        visualize_results(original_preds, int_preds, targets, args.output_dir)
        visualize_samples(test_loader, original_model, int_model, device, scale, args.output_dir)


if __name__ == '__main__':
    main()
