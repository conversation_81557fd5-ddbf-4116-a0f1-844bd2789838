# Integer-based LeNet-5 Inference for MNIST

This project implements an integer-based LeNet-5 neural network for MNIST digit classification using the quantized weights and biases extracted from a pre-trained model.

## Overview

The implementation consists of two main components:

1. `int_inference.py`: A PyTorch-based implementation of the LeNet-5 architecture that uses integer weights and biases.
2. `test_int_inference.py`: A script to test the integer-based network and compare its results with the original quantized model.

## Implementation Details

### Integer-based LeNet-5 Network

The integer-based LeNet-5 network is implemented in `int_inference.py`. It loads the integer weights and biases from a JSON file and uses them to perform inference on MNIST images.

Key features:
- Loads integer weights and biases from a JSON file
- Uses PyTorch's neural network modules for the forward pass
- Converts floating-point inputs to integers using a scale factor

### Testing Script

The testing script in `test_int_inference.py` compares the results of the integer-based network with the original quantized model. It loads the MNIST test dataset, runs inference on both models, and calculates accuracy metrics.

Key features:
- Loads the original quantized model and the integer-based model
- Converts floating-point inputs to integers for the integer-based model
- Calculates accuracy metrics and agreement between the two models
- Visualizes sample predictions (optional)

## Results

The integer-based LeNet-5 network achieves approximately 76.80% accuracy on the MNIST test set, compared to 98.60% for the original quantized model. The agreement between the two models is around 82%.

The accuracy gap is expected due to:
- Integer arithmetic vs. floating-point arithmetic
- Differences in quantization schemes
- Potential differences in handling quantization scales

## Usage

To test the integer-based network:

```bash
python test_int_inference.py --checkpoint checkpoints/model_best.pth --params-file extracted_params/parameters.json
```

Optional arguments:
- `--batch-size`: Batch size for testing (default: 100)
- `--output-dir`: Directory to save results (default: results/int_inference)
- `--visualize`: Visualize results (default: False)
- `--num-samples`: Number of samples to test (default: 1000, use -1 for all)

## Requirements

- Python 3.6+
- PyTorch
- NumPy
- Matplotlib
- scikit-learn
- tqdm

## Future Improvements

Potential improvements to increase accuracy:
- Implement a more accurate quantization scheme
- Handle quantization scales more precisely
- Use fixed-point arithmetic for better precision
- Implement layer-wise quantization parameters
