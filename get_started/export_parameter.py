#!/usr/bin/env python3
"""
Export Parameters from Quantized ONNX Model
===========================================
This script extracts integer weights and biases from a quantized ONNX model.
It specifically targets models exported with Brevitas' QCDQ format.

Usage:
    python export_parameter.py --model exported_models/lenet5_qcdq.onnx --output-dir extracted_params
"""

import os
import argparse
import json
import numpy as np
import onnx
from onnx import numpy_helper
from collections import defaultdict


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Extract integer parameters from ONNX model')
    parser.add_argument('--model', type=str, default='exported_models/lenet5_qcdq.onnx',
                        help='Path to ONNX model file')
    parser.add_argument('--output-dir', type=str, default='extracted_params',
                        help='Directory to save extracted parameters')
    parser.add_argument('--format', type=str, choices=['json', 'npy', 'both'], default='both',
                        help='Output format: json, npy, or both')
    return parser.parse_args()


def extract_parameters(model_path):
    """
    Extract integer weights and biases from ONNX model.

    Args:
        model_path: Path to the ONNX model file

    Returns:
        dict: Dictionary containing extracted parameters organized by layer
    """
    # Load the ONNX model
    model = onnx.load(model_path)
    graph = model.graph

    # Dictionary to store extracted parameters
    parameters = {}

    # Extract initializers (weights, biases, scales, etc.)
    print("Extracting parameters from ONNX model...")

    # First, identify all weight and bias tensors
    weight_tensors = {}
    bias_tensors = {}
    scale_tensors = {}

    # Process all initializers to find the quantized weights and biases
    for initializer in graph.initializer:
        try:
            name = initializer.name
            tensor = numpy_helper.to_array(initializer)

            # Extract layer name from tensor name
            parts = name.split('/')
            if len(parts) < 2:
                continue

            layer_name = parts[1]  # e.g., 'conv1', 'fc1', etc.

            # Find weight tensors (quantized integer weights)
            if 'weight_quant/export_handler/Constant_1_output_0' in name:
                weight_tensors[layer_name] = {
                    'data': tensor,
                    'shape': tensor.shape,
                    'dtype': str(tensor.dtype)
                }
                print(f"Found {layer_name} weight: shape={tensor.shape}, dtype={tensor.dtype}")

            # Find bias tensors (quantized integer biases)
            elif 'bias_quant/export_handler/Constant_output_0' in name:
                bias_tensors[layer_name] = {
                    'data': tensor,
                    'shape': tensor.shape,
                    'dtype': str(tensor.dtype)
                }
                print(f"Found {layer_name} bias: shape={tensor.shape}, dtype={tensor.dtype}")

            # Find scale tensors for weights
            elif 'weight_quant/export_handler/Constant_output_0' in name and tensor.size == 1:
                scale_tensors[f"{layer_name}_weight"] = {
                    'data': tensor,
                    'shape': tensor.shape,
                    'dtype': str(tensor.dtype)
                }
                print(f"Found {layer_name} weight scale: {tensor.item()}")

        except Exception as e:
            print(f"Error processing tensor {initializer.name}: {e}")

    # Combine weights and biases by layer
    for layer_name in set(list(weight_tensors.keys()) + list(bias_tensors.keys())):
        parameters[layer_name] = {}

        if layer_name in weight_tensors:
            parameters[layer_name]['weight'] = weight_tensors[layer_name]

        if layer_name in bias_tensors:
            parameters[layer_name]['bias'] = bias_tensors[layer_name]

        if f"{layer_name}_weight" in scale_tensors:
            parameters[layer_name]['weight_scale'] = scale_tensors[f"{layer_name}_weight"]

    return parameters


def save_parameters(parameters, output_dir, format='both'):
    """
    Save extracted parameters to files.

    Args:
        parameters: Dictionary of extracted parameters
        output_dir: Directory to save parameter files
        format: Output format ('json', 'npy', or 'both')
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Save parameters in specified format
    if format in ['json', 'both']:
        # Convert numpy arrays to lists for JSON serialization
        json_params = {}
        for layer_name, layer_params in parameters.items():
            json_params[layer_name] = {}
            for param_type, param_data in layer_params.items():
                json_params[layer_name][param_type] = {
                    'data': param_data['data'].tolist(),
                    'shape': param_data['shape'],
                    'dtype': param_data['dtype']
                }

        # Save to JSON file
        json_path = os.path.join(output_dir, 'parameters.json')
        with open(json_path, 'w') as f:
            json.dump(json_params, f, indent=2)
        print(f"Saved parameters to {json_path}")

    if format in ['npy', 'both']:
        # Save each parameter as a separate .npy file
        for layer_name, layer_params in parameters.items():
            layer_dir = os.path.join(output_dir, layer_name)
            os.makedirs(layer_dir, exist_ok=True)

            for param_type, param_data in layer_params.items():
                npy_path = os.path.join(layer_dir, f"{param_type}.npy")
                np.save(npy_path, param_data['data'])
                print(f"Saved {layer_name} {param_type} to {npy_path}")

    # Save parameters in a hardware-friendly format (C header files)
    hw_dir = os.path.join(output_dir, 'hardware')
    os.makedirs(hw_dir, exist_ok=True)

    # Create a header file for each layer
    for layer_name, layer_params in parameters.items():
        header_path = os.path.join(hw_dir, f"{layer_name}_params.h")

        with open(header_path, 'w') as f:
            f.write(f"// {layer_name} parameters for hardware implementation\n")
            f.write("#ifndef __" + f"{layer_name.upper()}_PARAMS_H__\n")
            f.write("#define __" + f"{layer_name.upper()}_PARAMS_H__\n\n")

            # Include standard headers
            f.write("#include <stdint.h>\n\n")

            # Write weight parameters
            if 'weight' in layer_params:
                weight_data = layer_params['weight']['data']
                weight_shape = layer_params['weight']['shape']
                weight_dtype = layer_params['weight']['dtype']

                # Determine C data type based on numpy dtype
                c_type = "int8_t" if "int8" in weight_dtype else "int32_t"

                # Create array dimensions string
                dims = ''.join([f"[{d}]" for d in weight_shape])

                # Write weight array declaration
                f.write(f"// Weight shape: {weight_shape}\n")
                f.write(f"const {c_type} {layer_name}_weights{dims} = {{\n")

                # Format the weight data as a C array
                if len(weight_shape) == 1:
                    # 1D array
                    f.write("    ")
                    for i, val in enumerate(weight_data):
                        f.write(f"{val}")
                        if i < len(weight_data) - 1:
                            f.write(", ")
                        if (i + 1) % 8 == 0 and i < len(weight_data) - 1:
                            f.write("\n    ")
                else:
                    # Multi-dimensional array
                    flat_data = weight_data.flatten()
                    f.write("    ")
                    for i, val in enumerate(flat_data):
                        f.write(f"{val}")
                        if i < len(flat_data) - 1:
                            f.write(", ")
                        if (i + 1) % 8 == 0 and i < len(flat_data) - 1:
                            f.write("\n    ")

                f.write("\n};\n\n")

            # Write bias parameters
            if 'bias' in layer_params:
                bias_data = layer_params['bias']['data']
                bias_shape = layer_params['bias']['shape']
                bias_dtype = layer_params['bias']['dtype']

                # Determine C data type based on numpy dtype
                c_type = "int32_t"  # Biases are typically int32

                # Create array dimensions string
                dims = ''.join([f"[{d}]" for d in bias_shape])

                # Write bias array declaration
                f.write(f"// Bias shape: {bias_shape}\n")
                f.write(f"const {c_type} {layer_name}_bias{dims} = {{\n")

                # Format the bias data as a C array
                f.write("    ")
                for i, val in enumerate(bias_data):
                    f.write(f"{val}")
                    if i < len(bias_data) - 1:
                        f.write(", ")
                    if (i + 1) % 8 == 0 and i < len(bias_data) - 1:
                        f.write("\n    ")

                f.write("\n};\n\n")

            # Write scale parameters if available
            if 'weight_scale' in layer_params:
                scale_val = layer_params['weight_scale']['data'].item()
                f.write(f"// Weight scale factor\n")
                f.write(f"const float {layer_name}_weight_scale = {scale_val}f;\n\n")

            f.write("#endif // __" + f"{layer_name.upper()}_PARAMS_H__\n")

        print(f"Saved hardware parameters to {header_path}")

    # Create a summary header file with all parameters
    summary_path = os.path.join(hw_dir, "lenet5_params.h")
    with open(summary_path, 'w') as f:
        f.write("// LeNet-5 Quantized Neural Network Parameters\n")
        f.write("// Auto-generated from ONNX model\n\n")
        f.write("#ifndef __LENET5_PARAMS_H__\n")
        f.write("#define __LENET5_PARAMS_H__\n\n")

        # Include all layer parameter headers
        for layer_name in parameters.keys():
            f.write(f"#include \"{layer_name}_params.h\"\n")

        f.write("\n// Layer dimensions and other constants\n")

        # Add network architecture constants
        f.write("#define INPUT_CHANNELS 1\n")
        f.write("#define INPUT_HEIGHT 28\n")
        f.write("#define INPUT_WIDTH 28\n\n")

        # Add layer-specific constants based on extracted parameters
        for layer_name, layer_params in parameters.items():
            if 'weight' in layer_params:
                weight_shape = layer_params['weight']['shape']

                if 'conv' in layer_name:
                    # Convolutional layer
                    if len(weight_shape) == 4:  # [out_channels, in_channels, kernel_h, kernel_w]
                        f.write(f"// {layer_name} dimensions\n")
                        f.write(f"#define {layer_name.upper()}_OUT_CHANNELS {weight_shape[0]}\n")
                        f.write(f"#define {layer_name.upper()}_IN_CHANNELS {weight_shape[1]}\n")
                        f.write(f"#define {layer_name.upper()}_KERNEL_SIZE {weight_shape[2]}\n")
                        f.write(f"#define {layer_name.upper()}_PADDING 2\n")
                        f.write(f"#define {layer_name.upper()}_STRIDE 1\n\n")

                elif 'fc' in layer_name:
                    # Fully connected layer
                    if len(weight_shape) == 2:  # [out_features, in_features]
                        f.write(f"// {layer_name} dimensions\n")
                        f.write(f"#define {layer_name.upper()}_OUT_FEATURES {weight_shape[0]}\n")
                        f.write(f"#define {layer_name.upper()}_IN_FEATURES {weight_shape[1]}\n\n")

        f.write("#endif // __LENET5_PARAMS_H__\n")

    print(f"Saved summary parameters to {summary_path}")


def print_parameter_summary(parameters):
    """
    Print a summary of the extracted parameters to the console.

    Args:
        parameters: Dictionary of extracted parameters
    """
    print("\n" + "="*80)
    print("PARAMETER SUMMARY")
    print("="*80)

    total_weights = 0
    total_biases = 0

    for layer_name, layer_params in parameters.items():
        print(f"\nLayer: {layer_name}")
        print("-" * 40)

        if 'weight' in layer_params:
            weight_data = layer_params['weight']['data']
            weight_shape = layer_params['weight']['shape']
            weight_dtype = layer_params['weight']['dtype']
            num_weights = weight_data.size
            total_weights += num_weights

            print(f"  Weights: shape={weight_shape}, dtype={weight_dtype}, count={num_weights}")
            print(f"  Weight stats: min={weight_data.min()}, max={weight_data.max()}, "
                  f"mean={weight_data.mean():.4f}, std={weight_data.std():.4f}")

        if 'bias' in layer_params:
            bias_data = layer_params['bias']['data']
            bias_shape = layer_params['bias']['shape']
            bias_dtype = layer_params['bias']['dtype']
            num_biases = bias_data.size
            total_biases += num_biases

            print(f"  Biases: shape={bias_shape}, dtype={bias_dtype}, count={num_biases}")
            print(f"  Bias stats: min={bias_data.min()}, max={bias_data.max()}, "
                  f"mean={bias_data.mean():.4f}, std={bias_data.std():.4f}")

        if 'weight_scale' in layer_params:
            scale_val = layer_params['weight_scale']['data'].item()
            print(f"  Weight scale: {scale_val}")

    print("\n" + "="*80)
    print(f"Total parameters: {total_weights + total_biases}")
    print(f"  - Weights: {total_weights}")
    print(f"  - Biases: {total_biases}")
    print("="*80 + "\n")


def main():
    """Main function."""
    args = parse_args()

    # Extract parameters from ONNX model
    parameters = extract_parameters(args.model)

    # Print parameter summary
    print_parameter_summary(parameters)

    # Save parameters to files
    save_parameters(parameters, args.output_dir, args.format)

    print("Parameter extraction complete!")


if __name__ == "__main__":
    main()
